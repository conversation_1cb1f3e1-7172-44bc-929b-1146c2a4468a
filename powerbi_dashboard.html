<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optiven Marketing Performance Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .header-left p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .header-right {
            text-align: right;
        }
        
        .period-info {
            font-size: 16px;
            font-weight: 500;
        }
        
        .last-updated {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .dashboard-body {
            padding: 30px;
        }
        
        .kpi-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .kpi-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .kpi-card.success { border-left-color: #27ae60; }
        .kpi-card.warning { border-left-color: #f39c12; }
        .kpi-card.danger { border-left-color: #e74c3c; }
        
        .kpi-title {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .kpi-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .kpi-subtitle {
            font-size: 12px;
            color: #95a5a6;
        }
        
        .kpi-trend {
            display: flex;
            align-items: center;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .trend-up { color: #27ae60; }
        .trend-down { color: #e74c3c; }
        .trend-neutral { color: #7f8c8d; }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .performance-donut {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin: 20px 0;
        }
        
        .donut-chart {
            width: 200px;
            height: 200px;
        }
        
        .donut-center {
            position: absolute;
            text-align: center;
        }
        
        .donut-percentage {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .donut-label {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .performers-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .performers-table th {
            background: #f8f9fa;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .performers-table td {
            padding: 12px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .performer-name {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .performance-badge {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }
        
        .badge-excellent { background: #d5f4e6; color: #27ae60; }
        .badge-good { background: #fef9e7; color: #f39c12; }
        .badge-poor { background: #fadbd8; color: #e74c3c; }
        
        .distribution-bars {
            margin: 20px 0;
        }
        
        .distribution-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .distribution-label {
            width: 140px;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .distribution-bar {
            flex: 1;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            margin: 0 15px;
            position: relative;
            overflow: hidden;
        }
        
        .distribution-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.8s ease;
        }
        
        .fill-excellent { background: linear-gradient(90deg, #27ae60, #2ecc71); }
        .fill-good { background: linear-gradient(90deg, #f39c12, #f1c40f); }
        .fill-average { background: linear-gradient(90deg, #e67e22, #f39c12); }
        .fill-poor { background: linear-gradient(90deg, #e74c3c, #c0392b); }
        
        .distribution-value {
            width: 50px;
            text-align: right;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .trend-chart {
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-style: italic;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .summary-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }
        
        .summary-stat {
            text-align: center;
        }
        
        .summary-stat-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .summary-stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .action-items {
            background: #fff;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .action-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .action-item.urgent { border-left-color: #e74c3c; }
        .action-item.warning { border-left-color: #f39c12; }
        .action-item.success { border-left-color: #27ae60; }
        
        .action-icon {
            margin-right: 15px;
            font-size: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="header-left">
                <h1>📊 Optiven Marketing Performance Dashboard</h1>
                <p>Real-time insights into marketer performance and target achievement</p>
            </div>
            <div class="header-right">
                <div class="period-info">May 2025</div>
                <div class="last-updated">Last updated: 2 hours ago</div>
            </div>
        </div>
        
        <div class="dashboard-body">
            <!-- Executive Summary Card -->
            <div class="summary-card">
                <div class="summary-title">🎯 Executive Summary</div>
                <div class="summary-stats">
                    <div class="summary-stat">
                        <div class="summary-stat-value">87.3%</div>
                        <div class="summary-stat-label">Overall Performance</div>
                    </div>
                    <div class="summary-stat">
                        <div class="summary-stat-value">142</div>
                        <div class="summary-stat-label">Active Marketers</div>
                    </div>
                    <div class="summary-stat">
                        <div class="summary-stat-value">+12.4%</div>
                        <div class="summary-stat-label">Month-over-Month</div>
                    </div>
                    <div class="summary-stat">
                        <div class="summary-stat-value">KES 2.4M</div>
                        <div class="summary-stat-label">Target Gap</div>
                    </div>
                </div>
            </div>
            
            <!-- Key Performance Indicators -->
            <div class="kpi-row">
                <div class="kpi-card success">
                    <div class="kpi-title">📈 Current Period Performance</div>
                    <div class="kpi-value">87.3%</div>
                    <div class="kpi-subtitle">Target Achievement</div>
                    <div class="kpi-trend trend-up">
                        📈 +12.4% vs last month
                    </div>
                </div>
                
                <div class="kpi-card">
                    <div class="kpi-title">🎯 Total MIB Achieved</div>
                    <div class="kpi-value">KES 18.7M</div>
                    <div class="kpi-subtitle">of KES 21.4M target</div>
                    <div class="kpi-trend trend-up">
                        💰 KES 2.3M increase
                    </div>
                </div>
                
                <div class="kpi-card warning">
                    <div class="kpi-title">⚠️ Target Gap</div>
                    <div class="kpi-value">KES 2.7M</div>
                    <div class="kpi-subtitle">12.7% below target</div>
                    <div class="kpi-trend trend-down">
                        📉 -3.2% vs last month
                    </div>
                </div>
                
                <div class="kpi-card success">
                    <div class="kpi-title">🏆 Team Achievement Rate</div>
                    <div class="kpi-value">64%</div>
                    <div class="kpi-subtitle">Marketers at 100%+</div>
                    <div class="kpi-trend trend-up">
                        👥 +8 marketers
                    </div>
                </div>
                
                <div class="kpi-card">
                    <div class="kpi-title">💼 Total Sales Achieved</div>
                    <div class="kpi-value">KES 156M</div>
                    <div class="kpi-subtitle">92.4% of target</div>
                    <div class="kpi-trend trend-up">
                        💎 +KES 18M increase
                    </div>
                </div>
                
                <div class="kpi-card">
                    <div class="kpi-title">💰 Commission Payable</div>
                    <div class="kpi-value">KES 4.2M</div>
                    <div class="kpi-subtitle">This period</div>
                    <div class="kpi-trend trend-neutral">
                        📊 Stable payout
                    </div>
                </div>
            </div>
            
            <!-- Main Content Grid -->
            <div class="content-grid">
                <!-- Performance Distribution -->
                <div class="chart-container">
                    <div class="chart-title">📊 Performance Distribution</div>
                    <div class="distribution-bars">
                        <div class="distribution-item">
                            <div class="distribution-label">Exceeding (100%+)</div>
                            <div class="distribution-bar">
                                <div class="distribution-fill fill-excellent" style="width: 64%"></div>
                            </div>
                            <div class="distribution-value">91</div>
                        </div>
                        <div class="distribution-item">
                            <div class="distribution-label">Near Target (80-99%)</div>
                            <div class="distribution-bar">
                                <div class="distribution-fill fill-good" style="width: 21%"></div>
                            </div>
                            <div class="distribution-value">30</div>
                        </div>
                        <div class="distribution-item">
                            <div class="distribution-label">Average (50-79%)</div>
                            <div class="distribution-bar">
                                <div class="distribution-fill fill-average" style="width: 11%"></div>
                            </div>
                            <div class="distribution-value">15</div>
                        </div>
                        <div class="distribution-item">
                            <div class="distribution-label">Below Target (<50%)</div>
                            <div class="distribution-bar">
                                <div class="distribution-fill fill-poor" style="width: 4%"></div>
                            </div>
                            <div class="distribution-value">6</div>
                        </div>
                    </div>
                </div>
                
                <!-- Overall Performance Gauge -->
                <div class="chart-container">
                    <div class="chart-title">🎯 Overall Performance</div>
                    <div class="performance-donut">
                        <svg class="donut-chart" viewBox="0 0 120 120">
                            <circle cx="60" cy="60" r="50" fill="none" stroke="#ecf0f1" stroke-width="20"/>
                            <circle cx="60" cy="60" r="50" fill="none" stroke="#27ae60" stroke-width="20" 
                                    stroke-dasharray="274.89" stroke-dashoffset="35.74" 
                                    transform="rotate(-90 60 60)" stroke-linecap="round"/>
                        </svg>
                        <div class="donut-center">
                            <div class="donut-percentage">87.3%</div>
                            <div class="donut-label">Target Achieved</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Performers Tables -->
            <div class="content-grid">
                <!-- Top Performers -->
                <div class="chart-container">
                    <div class="chart-title">🏆 Top Performers</div>
                    <table class="performers-table">
                        <thead>
                            <tr>
                                <th>Marketer</th>
                                <th>Team</th>
                                <th>Performance</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="performer-name">Sarah Kiprotich</td>
                                <td>Nairobi Central</td>
                                <td><span class="performance-badge badge-excellent">147.2%</span></td>
                                <td>📈</td>
                            </tr>
                            <tr>
                                <td class="performer-name">John Mwangi</td>
                                <td>Thika</td>
                                <td><span class="performance-badge badge-excellent">134.8%</span></td>
                                <td>📈</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Grace Wanjiku</td>
                                <td>Kiambu</td>
                                <td><span class="performance-badge badge-excellent">128.5%</span></td>
                                <td>📈</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Peter Ochieng</td>
                                <td>Nakuru</td>
                                <td><span class="performance-badge badge-excellent">122.1%</span></td>
                                <td>➡️</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Mary Njeri</td>
                                <td>Mombasa</td>
                                <td><span class="performance-badge badge-excellent">118.9%</span></td>
                                <td>📈</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Bottom Performers / Attention Needed -->
                <div class="chart-container">
                    <div class="chart-title">⚠️ Needs Attention</div>
                    <table class="performers-table">
                        <thead>
                            <tr>
                                <th>Marketer</th>
                                <th>Team</th>
                                <th>Performance</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="performer-name">David Kamau</td>
                                <td>Eldoret</td>
                                <td><span class="performance-badge badge-poor">23.4%</span></td>
                                <td>📉</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Jane Akinyi</td>
                                <td>Kisumu</td>
                                <td><span class="performance-badge badge-poor">31.2%</span></td>
                                <td>📉</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Samuel Kiptoo</td>
                                <td>Nairobi South</td>
                                <td><span class="performance-badge badge-poor">38.7%</span></td>
                                <td>➡️</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Ruth Wanjiru</td>
                                <td>Machakos</td>
                                <td><span class="performance-badge badge-poor">42.1%</span></td>
                                <td>📉</td>
                            </tr>
                            <tr>
                                <td class="performer-name">Eric Mutua</td>
                                <td>Nyeri</td>
                                <td><span class="performance-badge badge-poor">45.8%</span></td>
                                <td>📈</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Trend Analysis -->
            <div class="chart-container">
                <div class="chart-title">📈 Performance Trend Analysis</div>
                <div class="trend-chart">
                    <div>
                        <strong>Period-over-Period Growth: +12.4%</strong><br>
                        Previous Period: 75.2% | Current Period: 87.3%<br>
                        <em>Strong improvement trend - Performance increasing consistently</em>
                    </div>
                </div>
            </div>
            
            <!-- Action Items -->
            <div class="action-items">
                <div class="chart-title">🎯 Recommended Actions</div>
                
                <div class="action-item success">
                    <div class="action-icon">🏆</div>
                    <div>
                        <strong>Recognize Top Performers:</strong> Sarah Kiprotich, John Mwangi, and Grace Wanjiku are exceeding targets by 20%+. Consider promotion or incentive programs.
                    </div>
                </div>
                
                <div class="action-item urgent">
                    <div class="action-icon">🚨</div>
                    <div>
                        <strong>Immediate Intervention Needed:</strong> 6 marketers performing below 50%. Schedule coaching sessions with David Kamau and Jane Akinyi this week.
                    </div>
                </div>
                
                <div class="action-item warning">
                    <div class="action-icon">⚠️</div>
                    <div>
                        <strong>Target Gap Analysis:</strong> KES 2.7M shortfall. Focus on converting near-target performers (30 marketers at 80-99%) to exceed targets.
                    </div>
                </div>
                
                <div class="action-item">
                    <div class="action-icon">📈</div>
                    <div>
                        <strong>Trend Momentum:</strong> +12.4% growth is positive. Maintain current strategies and consider scaling successful approaches from top-performing teams.
                    </div>
                </div>
                
                <div class="action-item success">
                    <div class="action-icon">💰</div>
                    <div>
                        <strong>Commission Review:</strong> KES 4.2M commission payable. Ensure timely processing to maintain motivation among high performers.
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>