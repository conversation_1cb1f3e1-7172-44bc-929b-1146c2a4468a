'''
# Power BI Dashboard Layout Guide: Optiven R_E Investment Tracking

This guide outlines the suggested layout and visualizations for your Power BI dashboard, utilizing the DAX measures created in `Revenue_Dashboard_Measures.dax`.

**Dashboard Title:** Optiven Real Estate - Revenue & Project Performance Dashboard

**Data Tables (DirectQuery):**
*   `Optiven R_E$Investment Booking` (aliased as 'Bookings')
*   `Optiven R_E$Receipt Line` (aliased as 'Receipts')
*   `DateDimension` (calculated table)

**Relationships:**
*   `DateDimension[Date]` with `Optiven R_E$Investment Booking[Booking Date]` (Many-to-One, Single)
*   `DateDimension[Date]` with `Optiven R_E$Receipt Line[Payment Date]` (Many-to-One, Single)
*   `Optiven R_E$Investment Booking[Booking No]` with `Optiven R_E$Receipt Line[Lead File No_]` (One-to-Many, Single, assuming 'Booking No' is unique per booking and 'Lead File No_' can have multiple payments)

---

## I. Dashboard Pages

### Page 1: Revenue & Payments Overview

**Objective:** Provide a high-level view of revenue trends, payment status, and key performance indicators.

**Layout:**

*   **Row 1: KPIs (Cards)**
    *   `Total Booked Revenue`
    *   `Total Payments Received`
    *   `Overall Payment Balance`
    *   `Number of Active Bookings`
    *   `Number of Dropped Bookings`
    *   `Drop Rate %` (Measure: Dropped Bookings / Total Bookings)

*   **Row 2: Revenue & Payment Trends (Line Charts)**
    *   **Revenue Trend by Booking Date:**
        *   Axis: `DateDimension[TargetPeriodLabel]` (sorted by `DateDimension[TargetPeriodSort]`)
        *   Values: `Total Booked Revenue`
        *   Secondary Values (Optional): `Total Payments Received` (to see collection against booking)
    *   **Payments Trend by Payment Date:**
        *   Axis: `DateDimension[TargetPeriodLabel]` (sorted by `DateDimension[TargetPeriodSort]`)
        *   Values: `Total Payments Received`

*   **Row 3: Project & Payment Status (Bar/Column Charts & Table)**
    *   **Booked Revenue by Project:**
        *   Axis: `Optiven R_E$Investment Booking[Project Name]`
        *   Value: `Total Booked Revenue`
        *   (Consider Top N filter or slicer if many projects)
    *   **Payments Received by Project:**
        *   Axis: `Optiven R_E$Investment Booking[Project Name]` (or `Optiven R_E$Receipt Line` linked project name)
        *   Value: `Total Payments Received`
    *   **Lead File Payment Status (Table):**
        *   Columns:
            *   `Optiven R_E$Investment Booking[Booking No]`
            *   `Optiven R_E$Investment Booking[Member Name]`
            *   `Optiven R_E$Investment Booking[Project Name]`
            *   `Optiven R_E$Investment Booking[Property Price]`
            *   `Total Payments` (Measure)
            *   `Payment Balance` (Measure)
            *   `Payment Status` (Measure: "Fully Paid", "Partially Paid", "No Payment")
            *   `Last Payment Date` (Measure)
            *   `Dropped` (Yes/No)
            *   `Dropped On` (Date if applicable)
            *   `Restored` (Yes/No)
            *   `Restored On` (Date if applicable)

**Filters/Slicers for Page 1:**
*   `DateDimension[TargetPeriodLabel]` (for focused period analysis)
*   `Optiven R_E$Investment Booking[Project Name]`
*   `Optiven R_E$Investment Booking[Sales Person Name]`
*   `Optiven R_E$Investment Booking[Purchase Type]`

---

### Page 2: Project Performance Deep Dive

**Objective:** Analyze the performance of individual projects and sales channels.

**Layout:**

*   **Row 1: KPIs (Cards - Contextual to selected Project/Sales Person if filters are used)**
    *   `Total Booked Revenue`
    *   `Total Payments Received`
    *   `Number of Bookings`
    *   `Average Property Price` (Measure)

*   **Row 2: Project Comparison (Bar/Column Charts)**
    *   **Top/Bottom Projects by Revenue:**
        *   Axis: `Optiven R_E$Investment Booking[Project Name]`
        *   Value: `Total Booked Revenue`
        *   (Consider a toggle for Top N / Bottom N)
    *   **Top/Bottom Projects by Payments Received:**
        *   Axis: `Optiven R_E$Investment Booking[Project Name]`
        *   Value: `Total Payments Received`

*   **Row 3: Sales Performance (Bar/Column Charts & Table)**
    *   **Revenue by Sales Person:**
        *   Axis: `Optiven R_E$Investment Booking[Sales Person Name]`
        *   Value: `Total Booked Revenue`
    *   **Bookings by Campaign/Lead Source:**
        *   Axis: `Optiven R_E$Investment Booking[Campaign - Source of Lead]`
        *   Value: `Number of Bookings`
    *   **Project Details (Table - Drill-through target from project charts):**
        *   Columns:
            *   `Optiven R_E$Investment Booking[Booking No]`
            *   `Optiven R_E$Investment Booking[Member Name]`
            *   `Optiven R_E$Investment Booking[Plot No]`
            *   `Optiven R_E$Investment Booking[Property Price]`
            *   `Total Payments` (Measure)
            *   `Payment Balance` (Measure)
            *   `Optiven R_E$Investment Booking[Booking Date]`
            *   `Last Payment Date` (Measure)

**Filters/Slicers for Page 2:**
*   `DateDimension[Year]`
*   `DateDimension[MonthName]`
*   `Optiven R_E$Investment Booking[Project Name]`
*   `Optiven R_E$Investment Booking[Sales Person Name]`
*   `Optiven R_E$Investment Booking[Global Dimension 1 Code]` (if used for region/category)
*   `Optiven R_E$Investment Booking[Purchase Type]`

---

### Page 3: Lead File Status Analysis

**Objective:** Track and analyze dropped lead files and their impact on revenue.

**Layout:**

*   **Row 1: Drop Status KPIs (Cards)**
    *   `Total Dropped Lead Files`
    *   `Total Restored Lead Files`
    *   `Net Drop Rate %`
    *   `Revenue Impact of Drops` (Measure: Sum of Property Price for dropped files)
    *   `Average Time to Drop` (Days between Booking and Drop)

*   **Row 2: Drop Trends (Line/Column Charts)**
    *   **Drop Rate Trend:**
        *   Axis: `DateDimension[TargetPeriodLabel]`
        *   Values: `Number of Dropped Files`
        *   Secondary Values: `Drop Rate %`
    *   **Revenue Impact of Drops by Period:**
        *   Axis: `DateDimension[TargetPeriodLabel]`
        *   Values: `Lost Revenue Due to Drops`

*   **Row 3: Drop Analysis (Charts & Table)**
    *   **Drops by Project:**
        *   Axis: `Optiven R_E$Investment Booking[Project Name]`
        *   Values: `Number of Dropped Files`
        *   Secondary Values: `Project Drop Rate %`
    *   **Drop Reasons Analysis (if available):**
        *   Pie or Bar chart showing distribution of drop reasons
    *   **Dropped Files Details (Table):**
        *   Columns:
            *   `Optiven R_E$Investment Booking[Booking No]`
            *   `Optiven R_E$Investment Booking[Member Name]`
            *   `Optiven R_E$Investment Booking[Project Name]`
            *   `Optiven R_E$Investment Booking[Property Price]`
            *   `Total Payments Before Drop`
            *   `Dropped On`
            *   `Drop Posted On`
            *   `Days Active Before Drop`
            *   `Restored` (Yes/No)
            *   `Restored On`

**Filters/Slicers for Page 3:**
*   `DateDimension[TargetPeriodLabel]`
*   `Optiven R_E$Investment Booking[Project Name]`
*   `Optiven R_E$Investment Booking[Sales Person Name]`
*   `Drop Status` (All/Dropped/Restored)

---

## II. Key Visualizations & Considerations

1.  **Revenue Trend (Line Chart):**
    *   Use `DateDimension[TargetPeriodLabel]` on the X-axis, sorted by `TargetPeriodSort`.
    *   Show `Total Booked Revenue`. Consider adding `Total Payments Received` as a second line to visualize collection rate over time.
    *   Enable drill-down to `DateDimension[Year]`, `DateDimension[MonthName]`.

2.  **Project Performance (Bar/Column Chart):**
    *   Display `Total Booked Revenue` per `Optiven R_E$Investment Booking[Project Name]`.
    *   Enable "Top N" filtering to focus on best/worst performers.
    *   Allow drill-through to a detailed table showing individual bookings for the selected project.

3.  **Payment Status (Pie Chart or Stacked Bar Chart):**
    *   Visualize the distribution of `Number of Bookings` by `Payment Status` ("Fully Paid", "Partially Paid", "No Payment").
    *   Alternatively, show `Total Booked Revenue` segmented by `Payment Status`.

4.  **Lead File Details (Table):**
    *   This is crucial for operational tracking.
    *   Include key identifiers, financial summaries (`Property Price`, `Total Payments`, `Payment Balance`), and status.
    *   Use conditional formatting to highlight overdue balances or fully paid leads.

5.  **Sales Person Performance (Bar Chart):**
    *   `Total Booked Revenue` or `Number of Bookings` by `Optiven R_E$Investment Booking[Sales Person Name]`.

6.  **Campaign/Lead Source Effectiveness (Bar Chart):**
    *   `Number of Bookings` or `Total Booked Revenue` by `Optiven R_E$Investment Booking[Campaign - Source of Lead]`.

7.  **Drop Analysis Visualizations:**
    *   Use waterfall charts to show the progression from total bookings to active bookings (accounting for drops and restorations)
    *   Create a scatter plot comparing payment performance vs drop likelihood
    *   Consider adding warning indicators for leads at risk of dropping based on payment patterns

## III. Interactivity & User Experience

*   **Cross-Filtering:** Ensure charts and tables interact with each other. Clicking on a project in a bar chart should filter other visuals on the page for that project.
*   **Drill-Throughs:** Set up drill-throughs from summary charts (e.g., Project Revenue) to detailed tables (e.g., Bookings within that project).
*   **Slicers:** Provide intuitive slicers for key dimensions like Date (Target Period, Year, Month), Project Name, Sales Person.
*   **Tooltips:** Customize tooltips to show relevant additional information when hovering over data points (e.g., show `Total Payments` and `Payment Balance` when hovering over a project's revenue bar).
*   **Clear Naming:** Use clear and descriptive titles for all visuals and pages.

## IV. DAX Measure Implementation Notes

*   Ensure all measures from `Revenue_Dashboard_Measures.dax` are correctly implemented in your Power BI model.
*   Add specific measures for drop analysis:
    *   `Drop Rate %` = DIVIDE([Number of Dropped Files], [Total Bookings])
    *   `Days Active Before Drop` = DATEDIFF([Booking Date], [Dropped On], DAY)
    *   `Lost Revenue Due to Drops` = Calculate sum of [Property Price] for dropped files
    *   `Net Drop Rate` = ([Number of Dropped Files] - [Number of Restored Files]) / [Total Bookings]
*   Verify relationships between tables are active and correctly configured for DirectQuery.
*   Test measures with different filter contexts to ensure accuracy.

---

This layout guide provides a solid foundation. Feel free to adapt and customize it based on specific business requirements and user feedback.
''' 