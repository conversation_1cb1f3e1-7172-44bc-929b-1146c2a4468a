// =====================================================
// DATE DIMENSION TABLE DEFINITION
// For Optiven Real Estate Investment Tracking
// =====================================================

DateDimension =
VAR MinDate = DATE(2022, 1, 1)
VAR MaxDate = DATE(2026, 12, 31)
RETURN
ADDCOLUMNS(
    CALENDAR(MinDate, MaxDate),

    // ===== STANDARD DATE ATTRIBUTES =====
    "Year", YEAR([Date]),
    "Month", MONTH([Date]),
    "MonthName", FORMAT([Date], "MMMM"),
    "MonthNameShort", FORMAT([Date], "MMM"),
    "MonthNumber", MONTH([Date]),
    "Quarter", "Q" & QUARTER([Date]),
    "QuarterNumber", QUARTER([Date]),
    "DayOfWeek", WEEKDAY([Date]),
    "DayOfWeekName", FORMAT([Date], "DDDD"),
    "DayOfWeekShort", FORMAT([Date], "DDD"),
    "DayOfMonth", DAY([Date]),
    "DayOfYear", DATEDIFF(DATE(YEAR([Date]), 1, 1), [Date], DAY) + 1,
    "WeekOfYear", WEEKNUM([Date], 2), -- Monday as first day

    // ===== BUSINESS CALENDAR ATTRIBUTES =====
    "IsWeekend", WEEKDAY([Date]) IN {1, 7}, -- Sunday and Saturday
    "IsWeekday", NOT WEEKDAY([Date]) IN {1, 7},

    // ===== TARGET PERIOD LOGIC (21st-20th cycles) =====
    "TargetPeriodStart",
        IF(DAY([Date]) >= 21,
            DATE(YEAR([Date]), MONTH([Date]), 21),
            IF(MONTH([Date]) = 1,
                DATE(YEAR([Date])-1, 12, 21),
                DATE(YEAR([Date]), MONTH([Date])-1, 21)
            )
        ),

    "TargetPeriodEnd",
        IF(DAY([Date]) >= 21,
            IF(MONTH([Date]) = 12,
                DATE(YEAR([Date])+1, 1, 20),
                DATE(YEAR([Date]), MONTH([Date])+1, 20)
            ),
            DATE(YEAR([Date]), MONTH([Date]), 20)
        ),

    "TargetPeriodLabel",
        VAR PeriodStart =
            IF(DAY([Date]) >= 21,
                DATE(YEAR([Date]), MONTH([Date]), 21),
                IF(MONTH([Date]) = 1,
                    DATE(YEAR([Date])-1, 12, 21),
                    DATE(YEAR([Date]), MONTH([Date])-1, 21)
                )
            )
        VAR PeriodEnd =
            IF(DAY([Date]) >= 21,
                IF(MONTH([Date]) = 12,
                    DATE(YEAR([Date])+1, 1, 20),
                    DATE(YEAR([Date]), MONTH([Date])+1, 20)
                ),
                DATE(YEAR([Date]), MONTH([Date]), 20)
            )
        RETURN
            FORMAT(PeriodStart, "yyyy") & " " &
            FORMAT(PeriodStart, "MMM") & "-" &
            FORMAT(PeriodEnd, "MMM"),

    "TargetPeriodKey",
        VAR PeriodStart =
            IF(DAY([Date]) >= 21,
                DATE(YEAR([Date]), MONTH([Date]), 21),
                IF(MONTH([Date]) = 1,
                    DATE(YEAR([Date])-1, 12, 21),
                    DATE(YEAR([Date]), MONTH([Date])-1, 21)
                )
            )
        RETURN
            FORMAT(PeriodStart, "yyyyMM"),

    "TargetPeriodSort",
        IF(DAY([Date]) >= 21,
            (YEAR([Date]) * 100) + MONTH([Date]),
            IF(MONTH([Date]) = 1,
                (YEAR([Date])-1) * 100 + 12,
                (YEAR([Date]) * 100) + (MONTH([Date])-1)
            )
        ),

    // ===== ENHANCED TARGET PERIOD ATTRIBUTES =====
    "TargetPeriodDayNumber",
        VAR PeriodStart =
            IF(DAY([Date]) >= 21,
                DATE(YEAR([Date]), MONTH([Date]), 21),
                IF(MONTH([Date]) = 1,
                    DATE(YEAR([Date])-1, 12, 21),
                    DATE(YEAR([Date]), MONTH([Date])-1, 21)
                )
            )
        RETURN DATEDIFF(PeriodStart, [Date], DAY) + 1,

    "TargetPeriodWeek",
        VAR PeriodStart =
            IF(DAY([Date]) >= 21,
                DATE(YEAR([Date]), MONTH([Date]), 21),
                IF(MONTH([Date]) = 1,
                    DATE(YEAR([Date])-1, 12, 21),
                    DATE(YEAR([Date]), MONTH([Date])-1, 21)
                )
            )
        VAR DayInPeriod = DATEDIFF(PeriodStart, [Date], DAY) + 1
        RETURN CEILING(DayInPeriod / 7, 1),

    // ===== RELATIVE DATE FLAGS =====
    "IsToday", [Date] = TODAY(),
    "IsYesterday", [Date] = TODAY() - 1,
    "IsTomorrow", [Date] = TODAY() + 1,

    // Current week (Monday-Sunday)
    "IsCurrentWeek",
        VAR WeekStart = TODAY() - WEEKDAY(TODAY(), 2) + 1
        VAR WeekEnd = WeekStart + 6
        RETURN [Date] >= WeekStart && [Date] <= WeekEnd,

    "IsPreviousWeek",
        VAR WeekStart = TODAY() - WEEKDAY(TODAY(), 2) + 1 - 7
        VAR WeekEnd = WeekStart + 6
        RETURN [Date] >= WeekStart && [Date] <= WeekEnd,

    // Current month flags
    "IsCurrentMonth",
        YEAR([Date]) = YEAR(TODAY()) && MONTH([Date]) = MONTH(TODAY()),

    "IsPreviousMonth",
        VAR PrevMonth = EOMONTH(TODAY(), -1)
        RETURN YEAR([Date]) = YEAR(PrevMonth) && MONTH([Date]) = MONTH(PrevMonth),

    // Current target period flags
    "IsCurrentTargetPeriod",
        VAR TodayPeriodStart =
            IF(DAY(TODAY()) >= 21,
                DATE(YEAR(TODAY()), MONTH(TODAY()), 21),
                IF(MONTH(TODAY()) = 1,
                    DATE(YEAR(TODAY())-1, 12, 21),
                    DATE(YEAR(TODAY()), MONTH(TODAY())-1, 21)
                )
            )
        VAR TodayPeriodEnd =
            IF(DAY(TODAY()) >= 21,
                IF(MONTH(TODAY()) = 12,
                    DATE(YEAR(TODAY())+1, 1, 20),
                    DATE(YEAR(TODAY()), MONTH(TODAY())+1, 20)
                ),
                DATE(YEAR(TODAY()), MONTH(TODAY()), 20)
            )
        VAR DatePeriodStart =
            IF(DAY([Date]) >= 21,
                DATE(YEAR([Date]), MONTH([Date]), 21),
                IF(MONTH([Date]) = 1,
                    DATE(YEAR([Date])-1, 12, 21),
                    DATE(YEAR([Date]), MONTH([Date])-1, 21)
                )
            )
        RETURN DatePeriodStart = TodayPeriodStart,

    "IsPreviousTargetPeriod",
        VAR TodayPeriodStart =
            IF(DAY(TODAY()) >= 21,
                DATE(YEAR(TODAY()), MONTH(TODAY()), 21),
                IF(MONTH(TODAY()) = 1,
                    DATE(YEAR(TODAY())-1, 12, 21),
                    DATE(YEAR(TODAY()), MONTH(TODAY())-1, 21)
                )
            )
        VAR PrevPeriodStart =
            IF(MONTH(TodayPeriodStart) = 1,
                DATE(YEAR(TodayPeriodStart)-1, 12, 21),
                DATE(YEAR(TodayPeriodStart), MONTH(TodayPeriodStart)-1, 21)
            )
        VAR DatePeriodStart =
            IF(DAY([Date]) >= 21,
                DATE(YEAR([Date]), MONTH([Date]), 21),
                IF(MONTH([Date]) = 1,
                    DATE(YEAR([Date])-1, 12, 21),
                    DATE(YEAR([Date]), MONTH([Date])-1, 21)
                )
            )
        RETURN DatePeriodStart = PrevPeriodStart,

    // ===== ADDITIONAL USEFUL ATTRIBUTES =====
    "YearMonth", FORMAT([Date], "YYYY-MM"),
    "YearQuarter", FORMAT([Date], "YYYY") & "-Q" & QUARTER([Date]),
    "MonthYear", FORMAT([Date], "MMM YYYY"),
    "DateKey", YEAR([Date]) * 10000 + MONTH([Date]) * 100 + DAY([Date]),

    // Days from today (useful for filtering)
    "DaysFromToday", DATEDIFF(TODAY(), [Date], DAY),
    "WeeksFromToday", DATEDIFF(TODAY(), [Date], DAY) / 7,

    // Financial year (assuming April-March, adjust as needed)
    "FinancialYear",
        IF(MONTH([Date]) >= 4,
            YEAR([Date]),
            YEAR([Date]) - 1
        ),

    "FinancialQuarter",
        VAR FiscalMonth = IF(MONTH([Date]) >= 4, MONTH([Date]) - 3, MONTH([Date]) + 9)
        RETURN "FQ" & CEILING(FiscalMonth / 3, 1)
)

// =====================================================
// CLIENT TRACKING MEASURES
// For Optiven Real Estate Investment Tracking
// =====================================================

// ===== BASE CLIENT MEASURES =====

// Active Clients This Week (using date dimension)
Active Clients This Week =
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL(DateDimension),
        DateDimension[IsCurrentWeek] = TRUE
    ),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// Active Clients Previous Week
Active Clients Previous Week =
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL(DateDimension),
        DateDimension[IsPreviousWeek] = TRUE
    ),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// Active Clients This Month
Active Clients This Month =
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL(DateDimension),
        DateDimension[IsCurrentMonth] = TRUE
    ),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// Active Clients This Target Period
Active Clients Current Target Period =
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL(DateDimension),
        DateDimension[IsCurrentTargetPeriod] = TRUE
    ),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// Active Clients Previous Target Period
Active Clients Previous Target Period =
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL(DateDimension),
        DateDimension[IsPreviousTargetPeriod] = TRUE
    ),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// ===== NEW CLIENT ACQUISITION MEASURES =====

// New Clients This Week (not active in previous week)
New Clients This Week =
VAR CurrentWeekClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        FILTER(
            ALL(DateDimension),
            DateDimension[IsCurrentWeek] = TRUE
        ),
        FILTER(
            ALL('Optiven R_E$Investment Booking'),
            'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
            'Optiven R_E$Investment Booking'[Dropped] = 0 &&
            'Optiven R_E$Investment Booking'[Posted] = 1
        )
    )

VAR PreviousWeekClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        FILTER(
            ALL(DateDimension),
            DateDimension[IsPreviousWeek] = TRUE
        ),
        FILTER(
            ALL('Optiven R_E$Investment Booking'),
            'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
            'Optiven R_E$Investment Booking'[Dropped] = 0 &&
            'Optiven R_E$Investment Booking'[Posted] = 1
        )
    )

VAR NewClients = EXCEPT(CurrentWeekClients, PreviousWeekClients)

RETURN COUNTROWS(NewClients)

// New Clients This Target Period
New Clients Current Target Period =
VAR CurrentPeriodClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        FILTER(
            ALL(DateDimension),
            DateDimension[IsCurrentTargetPeriod] = TRUE
        ),
        FILTER(
            ALL('Optiven R_E$Investment Booking'),
            'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
            'Optiven R_E$Investment Booking'[Dropped] = 0 &&
            'Optiven R_E$Investment Booking'[Posted] = 1
        )
    )

VAR PreviousPeriodClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        FILTER(
            ALL(DateDimension),
            DateDimension[IsPreviousTargetPeriod] = TRUE
        ),
        FILTER(
            ALL('Optiven R_E$Investment Booking'),
            'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
            'Optiven R_E$Investment Booking'[Dropped] = 0 &&
            'Optiven R_E$Investment Booking'[Posted] = 1
        )
    )

VAR NewClients = EXCEPT(CurrentPeriodClients, PreviousPeriodClients)

RETURN COUNTROWS(NewClients)

// ===== CHANGE AND GROWTH MEASURES =====

// Weekly Change (Absolute)
Active Clients Weekly Change =
[Active Clients This Week] - [Active Clients Previous Week]

// Weekly Change (Percentage)
Active Clients Weekly Change % =
DIVIDE(
    [Active Clients Weekly Change],
    [Active Clients Previous Week],
    0
)

// Target Period Change (Absolute)
Active Clients Target Period Change =
[Active Clients Current Target Period] - [Active Clients Previous Target Period]

// Target Period Change (Percentage)
Active Clients Target Period Change % =
DIVIDE(
    [Active Clients Target Period Change],
    [Active Clients Previous Target Period],
    0
)

// ===== ENHANCED DISPLAY MEASURES =====

// Weekly Change Display with Enhanced Formatting
Weekly Change Display =
VAR Change = [Active Clients Weekly Change]
VAR PercentChange = [Active Clients Weekly Change %]
VAR Arrow =
    SWITCH(
        TRUE(),
        Change > 0, "📈",
        Change < 0, "📉",
        "➡️"
    )
VAR ChangeText =
    IF(Change >= 0, "+", "") & FORMAT(Change, "#,##0")
VAR PercentText =
    IF(
        ISBLANK(PercentChange) || PercentChange = 0,
        "",
        " (" & IF(PercentChange > 0, "+", "") & FORMAT(PercentChange, "0.0%") & ")"
    )

RETURN Arrow & " " & ChangeText & PercentText

// Target Period Change Display
Target Period Change Display =
VAR Change = [Active Clients Target Period Change]
VAR PercentChange = [Active Clients Target Period Change %]
VAR Arrow =
    SWITCH(
        TRUE(),
        Change > 0, "🚀",
        Change < 0, "⬇️",
        "⏸️"
    )
VAR ChangeText =
    IF(Change >= 0, "+", "") & FORMAT(Change, "#,##0")
VAR PercentText =
    IF(
        ISBLANK(PercentChange) || PercentChange = 0,
        "",
        " (" & IF(PercentChange > 0, "+", "") & FORMAT(PercentChange, "0.0%") & ")"
    )

RETURN Arrow & " " & ChangeText & PercentText

// ===== CLIENT RETENTION AND CHURN MEASURES =====

// Client Retention Rate (week-over-week)
Client Retention Rate Weekly =
VAR CurrentWeekClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        DateDimension[IsCurrentWeek] = TRUE,
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0,
        'Optiven R_E$Investment Booking'[Dropped] = 0,
        'Optiven R_E$Investment Booking'[Posted] = 1
    )

VAR PreviousWeekClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        DateDimension[IsPreviousWeek] = TRUE,
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0,
        'Optiven R_E$Investment Booking'[Dropped] = 0,
        'Optiven R_E$Investment Booking'[Posted] = 1
    )

VAR RetainedClients = INTERSECT(CurrentWeekClients, PreviousWeekClients)
VAR PreviousWeekCount = COUNTROWS(PreviousWeekClients)

RETURN
    IF(
        PreviousWeekCount = 0,
        BLANK(),
        DIVIDE(COUNTROWS(RetainedClients), PreviousWeekCount)
    )

// Client Churn Rate (clients who left)
Client Churn Rate Weekly =
VAR CurrentWeekClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        DateDimension[IsCurrentWeek] = TRUE,
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0,
        'Optiven R_E$Investment Booking'[Dropped] = 0,
        'Optiven R_E$Investment Booking'[Posted] = 1
    )

VAR PreviousWeekClients =
    CALCULATETABLE(
        VALUES('Optiven R_E$Investment Booking'[Member No]),
        DateDimension[IsPreviousWeek] = TRUE,
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0,
        'Optiven R_E$Investment Booking'[Dropped] = 0,
        'Optiven R_E$Investment Booking'[Posted] = 1
    )

VAR ChurnedClients = EXCEPT(PreviousWeekClients, CurrentWeekClients)
VAR PreviousWeekCount = COUNTROWS(PreviousWeekClients)

RETURN
    IF(
        PreviousWeekCount = 0,
        BLANK(),
        DIVIDE(COUNTROWS(ChurnedClients), PreviousWeekCount)
    )

// ===== SUMMARY METRICS =====

// Net Client Growth Rate
Net Client Growth Rate Weekly =
DIVIDE(
    [New Clients This Week] - COUNTROWS(
        EXCEPT(
            CALCULATETABLE(
                VALUES('Optiven R_E$Investment Booking'[Member No]),
                DateDimension[IsPreviousWeek] = TRUE,
                'Optiven R_E$Investment Booking'[Total Paid Measure] > 0,
                'Optiven R_E$Investment Booking'[Dropped] = 0,
                'Optiven R_E$Investment Booking'[Posted] = 1
            ),
            CALCULATETABLE(
                VALUES('Optiven R_E$Investment Booking'[Member No]),
                DateDimension[IsCurrentWeek] = TRUE,
                'Optiven R_E$Investment Booking'[Total Paid Measure] > 0,
                'Optiven R_E$Investment Booking'[Dropped] = 0,
                'Optiven R_E$Investment Booking'[Posted] = 1
            )
        )
    ),
    [Active Clients Previous Week],
    0
)

// ===== TREND ANALYSIS MEASURES =====

// Active Customers Trend (by Booking Date)
Active Customers Trend = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    ),
    USERELATIONSHIP(DateDimension[Date], 'Optiven R_E$Investment Booking'[Booking Date])
)

// Active Customers Trend (by Payment Date)
Active Customers Trend by Payment = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        'Optiven R_E$Investment Booking'[Posted] = 1
    ),
    USERELATIONSHIP(DateDimension[Date], 'Optiven R_E$Receipt Line'[Payment Date])
)

// Active Customers Trend 2025 (by Booking Date)
Active Customers Trend 2025 = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    FILTER(
        ALL('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Total Paid Measure] > 0 &&
        'Optiven R_E$Investment Booking'[Dropped] = 0 &&
        YEAR('Optiven R_E$Investment Booking'[Booking Date]) = 2025
    ),
    VALUES('Optiven R_E$Investment Booking'[Booking Date])
)