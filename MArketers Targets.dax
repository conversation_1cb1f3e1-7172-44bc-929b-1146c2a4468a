// === BASIC METRICS ===
Total Marketers = 
DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name])

Total Monthly Target = 
SUMX(
    'Optiven R_E$Marketer Targets',
    IF(
        ISBLANK('Optiven R_E$Marketer Targets'[Monthly Target]) 
        || NOT(ISNUMBER(VALUE('Optiven R_E$Marketer Targets'[Monthly Target]))),
        0,
        VALUE('Optiven R_E$Marketer Targets'[Monthly Target])
    )
)

Total MIB Achieved = 
SUMX(
    'Optiven R_E$Marketer Targets',
    IF(
        ISBLANK('Optiven R_E$Marketer Targets'[MIB Achieved]) 
        || NOT(ISNUMBER(VALUE('Optiven R_E$Marketer Targets'[MIB Achieved]))),
        0,
        VALUE('Optiven R_E$Marketer Targets'[MIB Achieved])
    )
)

Overall Performance % = 
VAR TargetValue = [Total Monthly Target]
VAR AchievedValue = [Total MIB Achieved]
RETURN
IF(
    ISBLANK(TargetValue) || TargetValue = 0,
    BLANK(),
    MIN(1000000, DIVIDE(AchievedValue, TargetValue, 0) * 100)
)

// === PERIOD-BASED MEASURES ===
Max Period Sort = 
MAXX(
    VALUES(DateDimension[TargetPeriodSort]),
    DateDimension[TargetPeriodSort]
)

Current Period Total Target = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CALCULATE(
    [Total Monthly Target],
    FILTER(
        ALLEXCEPT(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name]
        ),
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    )
)

Current Period Achievement = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CALCULATE(
    [Total MIB Achieved],
    FILTER(
        ALLEXCEPT(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name]
        ),
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    )
)

Current Period Performance = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR CurrentPeriodEnd = MAX('Optiven R_E$Marketer Targets'[Month End Date])
RETURN
CALCULATE(
    AVERAGE('Optiven R_E$Marketer Targets'[MIB Performance (_)]),
    'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart &&
    'Optiven R_E$Marketer Targets'[Month End Date] = CurrentPeriodEnd
)

Current Period MIB = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR CurrentPeriodEnd = MAX('Optiven R_E$Marketer Targets'[Month End Date])
RETURN
CALCULATE(
    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
    'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart &&
    'Optiven R_E$Marketer Targets'[Month End Date] = CurrentPeriodEnd
)

// === PERIOD COMPARISON MEASURES ===
Previous Period Sort = 
VAR MaxSort = [Max Period Sort]
RETURN
IF(ISBLANK(MaxSort), BLANK(), MaxSort - 1)

Previous Period Achievement = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
RETURN
CALCULATE(
    [Total MIB Achieved],
    'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
)

Previous Period Target = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
RETURN
CALCULATE(
    [Total Monthly Target],
    'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
)

Previous Period Performance = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR PrevTarget = 
    CALCULATE(
        [Total Monthly Target],
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
VAR PrevAchieved = 
    CALCULATE(
        [Total MIB Achieved],
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
RETURN
IF(
    ISBLANK(PrevTarget) || PrevTarget = 0,
    BLANK(),
    MIN(1000000, DIVIDE(PrevAchieved, PrevTarget, 0) * 100)
)

Period over Period Growth = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR Current = 
    CALCULATE(
        [Total MIB Achieved],
        'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart
    )
VAR Previous = 
    CALCULATE(
        [Total MIB Achieved],
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
RETURN
IF(
    ISBLANK(Previous) || Previous <= 0 || ISBLANK(Current),
    BLANK(),
    MIN(1000000, DIVIDE(Current - Previous, Previous, 0) * 100)
)

// === TEAM MEASURES ===
Avg Team Performance = 
VAR AvgPerf = AVERAGEX(
    'Optiven R_E$Marketer Targets',
    IF(
        ISBLANK('Optiven R_E$Marketer Targets'[MIB Performance (_)]) 
        || NOT(ISNUMBER(VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]))),
        0,
        VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)])
    )
)
RETURN
IF(ISBLANK(AvgPerf), BLANK(), MIN(1000000, AvgPerf))

Team Achievement Rate = 
VAR TotalCount = COUNTROWS('Optiven R_E$Marketer Targets')
VAR SuccessCount = COUNTROWS(
    FILTER('Optiven R_E$Marketer Targets', 
           VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) >= 100)
)
RETURN
IF(
    TotalCount = 0,
    BLANK(),
    MIN(100, DIVIDE(SuccessCount, TotalCount, 0) * 100)
)

// === TREND ANALYSIS MEASURES ===
Performance Trend = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR CurrentPerf = 
    CALCULATE(
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100,
        'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart
    )
VAR PrevPerf = 
    CALCULATE(
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100,
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
RETURN
IF(
    ISBLANK(CurrentPerf) || ISBLANK(PrevPerf),
    BLANK(),
    CurrentPerf - PrevPerf
)

// === TARGET GAP MEASURES ===
Target Gap = 
VAR Target = [Total Monthly Target]
VAR Achieved = [Total MIB Achieved]
RETURN
IF(
    ISBLANK(Target) || ISBLANK(Achieved),
    BLANK(),
    Target - Achieved
)

Target Gap % = 
VAR Gap = [Target Gap]
VAR Target = [Total Monthly Target]
RETURN
IF(
    ISBLANK(Gap) || ISBLANK(Target) || Target = 0,
    BLANK(),
    MIN(1000000, DIVIDE(Gap, Target, 0) * 100)
)

// === CURRENT PERIOD GAP MEASURES ===
Current Period Target Gap = 
VAR Target = [Current Period Total Target]
VAR Achieved = [Current Period Achievement]
RETURN
IF(
    ISBLANK(Target) || ISBLANK(Achieved),
    BLANK(),
    Target - Achieved
)

Current Period Target Gap % = 
VAR Gap = [Current Period Target Gap]
VAR Target = [Current Period Total Target]
RETURN
IF(
    ISBLANK(Gap) || ISBLANK(Target) || Target = 0,
    BLANK(),
    MIN(1000000, DIVIDE(Gap, Target, 0) * 100)
)

// === MARKETER PERFORMANCE MEASURES ===
Top Performer = 
TOPN(
    1,
    SUMMARIZE(
        'Optiven R_E$Marketer Targets',
        'Optiven R_E$Marketer Targets'[Marketer Name],
        "Performance", DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100
    ),
    [Performance],
    DESC
)[Marketer Name]

Regional Performance % = 
CALCULATE(
    [Overall Performance %],
    ALLEXCEPT('Optiven R_E$Marketer Targets', 'Optiven R_E$Marketer Targets'[Region])
)

Team Performance % = 
CALCULATE(
    [Overall Performance %],
    ALLEXCEPT('Optiven R_E$Marketer Targets', 'Optiven R_E$Marketer Targets'[Team])
)

Performance Category = 
SWITCH(
    TRUE(),
    [Overall Performance %] >= 100, "Exceeding Target",
    [Overall Performance %] >= 80, "Near Target",
    [Overall Performance %] >= 50, "Average",
    "Below Average"
)

// === MARKETER PERFORMANCE COUNTS ===
Marketers Exceeding Target = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        'Optiven R_E$Marketer Targets',
        VAR Performance = VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)])
        VAR CurrentPeriodCheck = 
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        RETURN
            CurrentPeriodCheck &&
            Performance >= 100 &&
            'Optiven R_E$Marketer Targets'[MIB Achieved] > 0
    )
)

Marketers Near Target = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        ALLEXCEPT(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name]
        ),
        VAR Target = [Current Period Total Target]
        VAR Achievement = [Current Period Achievement]
        VAR Performance = DIVIDE(Achievement, Target, 0) * 100
        RETURN
            Performance >= 80 
            && Performance < 100
            && Achievement > 0
    )
)

Marketers Below 50 = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        ALLEXCEPT(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name]
        ),
        VAR Target = [Current Period Total Target]
        VAR Achievement = [Current Period Achievement]
        VAR Performance = DIVIDE(Achievement, Target, 0) * 100
        RETURN
            Performance < 50
            && Achievement > 0
    )
)

Marketers With Zero MIB = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        ALLEXCEPT(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name]
        ),
        [Current Period Achievement] = 0
    )
)

// Performance Distribution
Marketers Performance Distribution = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR TotalMarketers = 
    CALCULATE(
        COUNTROWS('Optiven R_E$Marketer Targets'),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )
VAR ExceedingCount = [Marketers Exceeding Target]
VAR NearCount = [Marketers Near Target]
VAR BelowCount = [Marketers Below 50]
VAR ZeroCount = [Marketers With Zero MIB]
RETURN
"Total Marketers: " & FORMAT(TotalMarketers, "#,##0") & UNICHAR(10) &
"Exceeding Target (100%+): " & FORMAT(ExceedingCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(ExceedingCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) &
"Near Target (80-99%): " & FORMAT(NearCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(NearCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) &
"Below Target (<50%): " & FORMAT(BelowCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(BelowCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) &
"Zero Performance: " & FORMAT(ZeroCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(ZeroCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)"

// === INDIVIDUAL MARKETER MEASURES FOR MATRIX ===
Marketer Period Performance = 
VAR CurrentPeriodSort = [Current Target Period]
RETURN
CALCULATE(
    DIVIDE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
        0
    ) * 100,
    FILTER(
        ALL(DateDimension),
        DateDimension[TargetPeriodSort] = CurrentPeriodSort
    )
)

Current Target Period = 
MAXX(
    VALUES(DateDimension[TargetPeriodSort]),
    DateDimension[TargetPeriodSort]
)

Marketer Period Status = 
SWITCH(
    TRUE(),
    [Marketer Period Performance] >= 100, "Exceeding",
    [Marketer Period Performance] >= 80, "Near Target",
    [Marketer Period Performance] >= 50, "Average",
    "Below Target"
)

// === TARGET DISTRIBUTION MEASURES ===
Total Sales Target = 
SUMX(
    'Optiven R_E$Marketer Targets',
    IF(
        ISBLANK('Optiven R_E$Marketer Targets'[New Sales Target]) 
        || NOT(ISNUMBER(VALUE('Optiven R_E$Marketer Targets'[New Sales Target]))),
        0,
        VALUE('Optiven R_E$Marketer Targets'[New Sales Target])
    )
)

Total Sales Achieved = 
SUMX(
    'Optiven R_E$Marketer Targets',
    IF(
        ISBLANK('Optiven R_E$Marketer Targets'[News Sales Achieved]) 
        || NOT(ISNUMBER(VALUE('Optiven R_E$Marketer Targets'[News Sales Achieved]))),
        0,
        VALUE('Optiven R_E$Marketer Targets'[News Sales Achieved])
    )
)

Sales Performance % = 
VAR TargetValue = [Total Sales Target]
VAR AchievedValue = [Total Sales Achieved]
RETURN
IF(
    ISBLANK(TargetValue) || TargetValue = 0,
    BLANK(),
    MIN(1000000, DIVIDE(AchievedValue, TargetValue, 0) * 100)
)

Total Commission Payable = 
SUMX(
    'Optiven R_E$Marketer Targets',
    IF(
        ISBLANK('Optiven R_E$Marketer Targets'[Commission Payable TL (Amt)]) 
        || NOT(ISNUMBER(VALUE('Optiven R_E$Marketer Targets'[Commission Payable TL (Amt)]))),
        0,
        VALUE('Optiven R_E$Marketer Targets'[Commission Payable TL (Amt)])
    )
)

// === COMPREHENSIVE PERFORMANCE CARD ===
Performance Summary Card = 
VAR CurrentPerf = [Current Period Performance]
VAR PrevPerf = [Previous Period Performance]
VAR TrendValue = [Performance Trend]
VAR TrendIcon = 
    SWITCH(
        TRUE(),
        ISBLANK(TrendValue), "➖",
        TrendValue > 5, "↗️",
        TrendValue > 0, "↗️",
        TrendValue = 0, "➡️",
        TrendValue > -5, "↘️",
        "↘️"
    )
VAR TrendDescription = 
    SWITCH(
        TRUE(),
        ISBLANK(TrendValue), "No Change",
        TrendValue > 5, "Strong Improvement",
        TrendValue > 0, "Improving",
        TrendValue = 0, "Stable",
        TrendValue > -5, "Declining",
        "Strong Decline"
    )
VAR CurrentGap = [Current Period Target Gap]
VAR CurrentGapPercent = [Current Period Target Gap %]
RETURN
CONCATENATE(
    "PERFORMANCE SUMMARY" & UNICHAR(10) & UNICHAR(10),
    "Current Period: " & FORMAT(CurrentPerf, "#,##0.00") & "%" & UNICHAR(10) &
    "Previous Period: " & FORMAT(PrevPerf, "#,##0.00") & "%" & UNICHAR(10) &
    "Trend: " & TrendIcon & " " & 
    FORMAT(ABS(TrendValue), "#,##0.00") & "% " & 
    IF(TrendValue > 0, "Increase", IF(TrendValue < 0, "Decrease", "No Change")) &
    UNICHAR(10) & UNICHAR(10) &
    "Status: " & TrendDescription & UNICHAR(10) &
    "Target Gap: " & FORMAT(ABS(CurrentGap), "#,##0") & 
    " (" & FORMAT(ABS(CurrentGapPercent), "#,##0.00") & "%)" & UNICHAR(10) &
    SWITCH(
        TRUE(),
        CurrentGap > 0, "Below Target",
        CurrentGap < 0, "Exceeding Target",
        "On Target"
    )
)

// === TOP PERFORMERS AND CRITICAL CASES MEASURES ===
Top 5 Performers Table = 
ADDCOLUMNS(
    TOPN(
        5,
        SUMMARIZE(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name],
            'Optiven R_E$Marketer Targets'[Team],
            "Performance",
            DIVIDE(
                SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                0
            ) * 100
        ),
        [Performance],
        DESC
    ),
    "Trend", [Marketer Trend Icon]
)

Bottom 5 Performers Table = 
ADDCOLUMNS(
    TOPN(
        5,
        FILTER(
            SUMMARIZE(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name],
                'Optiven R_E$Marketer Targets'[Team],
                "Performance",
                DIVIDE(
                    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                    SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                    0
                ) * 100
            ),
            [Performance] > 0  // Exclude zero performers
        ),
        [Performance],
        ASC
    ),
    "Trend", [Marketer Trend Icon]
)

Combined Performance Table = 
UNION(
    [Top 5 Performers Table],
    [Bottom 5 Performers Table]
)

Marketer Performance % = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CALCULATE(
    DIVIDE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
        0
    ) * 100,
    FILTER(
        ALL('Optiven R_E$Marketer Targets'),
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    )
)

Marketer Trend Icon = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR CurrentPerf = 
    CALCULATE(
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100,
        'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart
    )
VAR PrevPerf = 
    CALCULATE(
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100,
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
VAR TrendValue = CurrentPerf - PrevPerf
RETURN
SWITCH(
    TRUE(),
    ISBLANK(TrendValue), "➡️",
    TrendValue > 5, "📈",
    TrendValue > 0, "📈",
    TrendValue = 0, "➡️",
    "📉"
)

Performance Status Format = 
VAR Performance = [Marketer Performance %]
RETURN
SWITCH(
    TRUE(),
    Performance >= 100, "#2ECC71",  // Green
    Performance >= 80, "#F1C40F",   // Yellow
    Performance >= 50, "#E67E22",   // Orange
    "#E74C3C"                       // Red
)

// === DEBUG MEASURES ===
Debug Exceeding Target = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR MarketersInPeriod = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            'Optiven R_E$Marketer Targets',
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )
VAR MarketersWithAchievement = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[MIB Achieved] > 0
        )
    )
RETURN
"Current Period: " & FORMAT(CurrentYearMonth, "0") & UNICHAR(10) &
"Total Marketers in Period: " & FORMAT(MarketersInPeriod, "0") & UNICHAR(10) &
"Marketers with Achievement: " & FORMAT(MarketersWithAchievement, "0")

Simple Top 10 Filter = 
VAR CurrentMarketer = SELECTEDVALUE('Optiven R_E$Marketer Targets'[Marketer Name])
VAR CurrentPerformance = 
    CALCULATE(
        AVERAGE('Optiven R_E$Marketer Targets'[MIB Performance (_)]),
        'Optiven R_E$Marketer Targets'[Marketer Name] = CurrentMarketer
    )

RETURN
IF(CurrentPerformance >= 85, 1, 0)

Test Performance = 
VAR CurrentMarketer = SELECTEDVALUE('Optiven R_E$Marketer Targets'[Marketer Name])
RETURN
CALCULATE(
    AVERAGE('Optiven R_E$Marketer Targets'[MIB Performance (_)]),
    'Optiven R_E$Marketer Targets'[Marketer Name] = CurrentMarketer
)

// === DASHBOARD VISUALIZATION MEASURES ===

// KPI Card Measures
Current Period KPI = 
VAR CurrentPerf = [Current Period Performance]
RETURN
FORMAT(CurrentPerf, "#,##0.0") & "%"

Total MIB KPI = 
VAR TotalMIB = [Total MIB Achieved]
RETURN
"KES " & FORMAT(TotalMIB, "#,##0,,M")

Target Gap KPI = 
VAR Gap = [Target Gap]
RETURN
"KES " & FORMAT(ABS(Gap), "#,##0,,M")

Team Achievement KPI = 
VAR ExceedingCount = [Marketers Exceeding Target]
VAR TotalCount = [Total Marketers]
RETURN
FORMAT(DIVIDE(ExceedingCount, TotalCount, 0) * 100, "#,##0") & "%"

Total Sales KPI = 
VAR Sales = [Total Sales Achieved]
RETURN
"KES " & FORMAT(Sales, "#,##0,,M")

Commission KPI = 
VAR Comm = [Total Commission Payable]
RETURN
"KES " & FORMAT(Comm, "#,##0,,M")

// Performance Distribution Table
Performance Distribution Table = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR CurrentPeriodEnd = MAX('Optiven R_E$Marketer Targets'[Month End Date])

// Count employees in each performance category
VAR ExceedingCount = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart &&
            'Optiven R_E$Marketer Targets'[Month End Date] = CurrentPeriodEnd &&
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) >= 100
        )
    )

VAR NearTargetCount = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart &&
            'Optiven R_E$Marketer Targets'[Month End Date] = CurrentPeriodEnd &&
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) >= 80 &&
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) < 100
        )
    )

VAR AverageCount = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart &&
            'Optiven R_E$Marketer Targets'[Month End Date] = CurrentPeriodEnd &&
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) >= 50 &&
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) < 80
        )
    )

VAR BelowTargetCount = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Month Start Date] = CurrentPeriodStart &&
            'Optiven R_E$Marketer Targets'[Month End Date] = CurrentPeriodEnd &&
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) < 50
        )
    )

RETURN
ADDCOLUMNS(
    UNION(
        ROW("Category", "Exceeding (100%+)", "Count", ExceedingCount),
        ROW("Category", "Near Target (80-99%)", "Count", NearTargetCount),
        ROW("Category", "Average (50-79%)", "Count", AverageCount),
        ROW("Category", "Below Target (<50%)", "Count", BelowTargetCount)
    ),
    "Color", 
    SWITCH(
        [Category],
        "Exceeding (100%+)", "#27ae60",
        "Near Target (80-99%)", "#f39c12",
        "Average (50-79%)", "#e67e22",
        "Below Target (<50%)", "#e74c3c"
    )
)

// Top Performers Table
Top Performers Table = 
ADDCOLUMNS(
    TOPN(
        5,
        SUMMARIZE(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name],
            'Optiven R_E$Marketer Targets'[Team],
            "Performance", 
            DIVIDE(
                SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                0
            ) * 100
        ),
        [Performance],
        DESC
    ),
    "Trend", [Marketer Trend Icon],
    "Status", 
    SWITCH(
        TRUE(),
        [Performance] >= 120, "🌟 Exceptional",
        [Performance] >= 100, "🏆 Exceeding",
        [Performance] >= 80, "✅ Near Target",
        "⚠️ Below Target"
    )
)

// Bottom Performers Table
Bottom Performers Table = 
ADDCOLUMNS(
    TOPN(
        5,
        FILTER(
            SUMMARIZE(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name],
                'Optiven R_E$Marketer Targets'[Team],
                "Performance", 
                DIVIDE(
                    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                    SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                    0
                ) * 100
            ),
            [Performance] > 0
        ),
        [Performance],
        ASC
    ),
    "Trend", [Marketer Trend Icon],
    "Status", 
    SWITCH(
        TRUE(),
        [Performance] >= 50, "⚠️ Needs Improvement",
        [Performance] >= 30, "🚨 Critical",
        "❌ At Risk"
    )
)

// Action Items List
Action Items List = 
VAR ZeroCount = [Marketers With Zero MIB]
VAR BelowCount = [Marketers Below 50]
VAR TopPerformer = [Top Performer]
VAR TargetGap = [Target Gap]
RETURN
UNION(
    ROW("Priority", "High", "Icon", "🏆", "Title", "Recognize Top Performers", 
        "Description", "Top performers exceeding targets by 20%+. Consider promotion or incentive programs."),
    ROW("Priority", "Critical", "Icon", "🚨", "Title", "Immediate Intervention Needed", 
        "Description", FORMAT(ZeroCount, "#,##0") & " marketers performing below 50%. Schedule coaching sessions."),
    ROW("Priority", "Medium", "Icon", "⚠️", "Title", "Target Gap Analysis", 
        "Description", "KES " & FORMAT(ABS(TargetGap), "#,##0,,M") & " shortfall. Focus on converting near-target performers."),
    ROW("Priority", "Low", "Icon", "📈", "Title", "Trend Momentum", 
        "Description", FORMAT([Period over Period Growth], "+#,##0.0") & "% growth. Maintain current strategies."),
    ROW("Priority", "Medium", "Icon", "💰", "Title", "Commission Review", 
        "Description", "KES " & FORMAT([Total Commission Payable], "#,##0,,M") & " commission payable. Ensure timely processing.")
)

// === PERIOD AND UPDATE INFO MEASURES ===

// Current Period Info
Current Period Info = 
VAR CurrentDate = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
RETURN
FORMAT(CurrentDate, "mmmm yyyy")

// Last Updated Info
Last Updated Info = 
VAR CurrentDate = NOW()
RETURN
"Last updated: " & FORMAT(CurrentDate, "mmmm dd, yyyy") & " at " & FORMAT(CurrentDate, "hh:mm AM/PM")

// Period Range Info
Period Range Info = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CALCULATE(
    VAR StartDate = MIN('Optiven R_E$Marketer Targets'[Month Start Date])
    VAR EndDate = MAX('Optiven R_E$Marketer Targets'[Month End Date])
    RETURN
        FORMAT(StartDate, "mmm dd") & " - " & FORMAT(EndDate, "mmm dd, yyyy"),
    FILTER(
        'Optiven R_E$Marketer Targets',
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    )
)

// Period Status
Period Status = 
VAR CurrentDate = NOW()
VAR EndDate = MAX('Optiven R_E$Marketer Targets'[Month End Date])
VAR DaysRemaining = DATEDIFF(CurrentDate, EndDate, DAY)
RETURN
SWITCH(
    TRUE(),
    DaysRemaining < 0, "Period Ended",
    DaysRemaining = 0, "Last Day",
    DaysRemaining <= 3, "Ending Soon",
    "In Progress"
)

// Period Progress
Period Progress = 
VAR CurrentDate = NOW()
VAR StartDate = MIN('Optiven R_E$Marketer Targets'[Month Start Date])
VAR EndDate = MAX('Optiven R_E$Marketer Targets'[Month End Date])
RETURN
DIVIDE(
    DATEDIFF(StartDate, CurrentDate, DAY),
    DATEDIFF(StartDate, EndDate, DAY),
    0
) * 100

// Executive Summary
Executive Summary = 
VAR CurrentPerf = [Current Period Performance]
VAR PrevPerf = [Previous Period Performance]
VAR TrendValue = [Performance Trend]
VAR TrendIcon = 
    SWITCH(
        TRUE(),
        ISBLANK(TrendValue), "➖",
        TrendValue > 5, "📈",
        TrendValue > 0, "↗️",
        TrendValue = 0, "➡️",
        TrendValue > -5, "↘️",
        "📉"
    )
VAR ExceedingCount = [Marketers Exceeding Target]
VAR NearCount = [Marketers Near Target]
VAR BelowCount = [Marketers Below 50]
VAR ZeroCount = [Marketers With Zero MIB]
VAR TotalMarketers = [Total Marketers]
VAR TargetGap = [Target Gap]
VAR SalesAchieved = [Total Sales Achieved]
VAR CommissionPayable = [Total Commission Payable]

RETURN
"EXECUTIVE SUMMARY" & UNICHAR(10) & UNICHAR(10) &
"Overall Performance: " & FORMAT(CurrentPerf, "#,##0.0") & "%" & UNICHAR(10) &
"Trend: " & TrendIcon & " " & FORMAT(ABS(TrendValue), "#,##0.0") & "% " & 
    IF(TrendValue > 0, "Increase", IF(TrendValue < 0, "Decrease", "No Change")) & UNICHAR(10) & UNICHAR(10) &
"Performance Distribution:" & UNICHAR(10) &
"• Exceeding Target: " & FORMAT(ExceedingCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(ExceedingCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) &
"• Near Target: " & FORMAT(NearCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(NearCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) &
"• Below Target: " & FORMAT(BelowCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(BelowCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) &
"• Zero Performance: " & FORMAT(ZeroCount, "#,##0") & 
    " (" & FORMAT(DIVIDE(ZeroCount, TotalMarketers, 0) * 100, "#,##0.0") & "%)" & UNICHAR(10) & UNICHAR(10) &
"Financial Overview:" & UNICHAR(10) &
"• Sales Achieved: KES " & FORMAT(SalesAchieved, "#,##0,,M") & UNICHAR(10) &
"• Target Gap: KES " & FORMAT(ABS(TargetGap), "#,##0,,M") & UNICHAR(10) &
"• Commission Payable: KES " & FORMAT(CommissionPayable, "#,##0,,M")

// Performance Distribution Visual
Performance Distribution Visual = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR TotalMarketers = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )
VAR ExceedingCount = [Marketers Exceeding Target]
VAR NearCount = [Marketers Near Target]
VAR BelowCount = [Marketers Below 50]
VAR ZeroCount = [Marketers With Zero MIB]

RETURN
UNION(
    ROW(
        "Category", "Exceeding Target (100%+)",
        "Count", ExceedingCount,
        "Percentage", DIVIDE(ExceedingCount, TotalMarketers, 0) * 100,
        "Color", "#27ae60",
        "Icon", "🏆",
        "Description", "Marketers exceeding their targets"
    ),
    ROW(
        "Category", "Near Target (80-99%)",
        "Count", NearCount,
        "Percentage", DIVIDE(NearCount, TotalMarketers, 0) * 100,
        "Color", "#f39c12",
        "Icon", "🎯",
        "Description", "Marketers close to achieving targets"
    ),
    ROW(
        "Category", "Below Target (50-79%)",
        "Count", BelowCount,
        "Percentage", DIVIDE(BelowCount, TotalMarketers, 0) * 100,
        "Color", "#e67e22",
        "Icon", "⚠️",
        "Description", "Marketers needing improvement"
    ),
    ROW(
        "Category", "Zero Performance (0%)",
        "Count", ZeroCount,
        "Percentage", DIVIDE(ZeroCount, TotalMarketers, 0) * 100,
        "Color", "#e74c3c",
        "Icon", "🚨",
        "Description", "Marketers with no achievement"
    )
)

// Active Marketers Measures
Active Marketers Count = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        ALL('Optiven R_E$Marketer Targets'),
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    )
)

Active Marketers List = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
CONCATENATEX(
    FILTER(
        SUMMARIZE(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Marketer Name],
            'Optiven R_E$Marketer Targets'[Team],
            'Optiven R_E$Marketer Targets'[Region],
            "Performance", 
            DIVIDE(
                SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                0
            ) * 100
        ),
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    ),
    'Optiven R_E$Marketer Targets'[Marketer Name] & 
    " (" & 'Optiven R_E$Marketer Targets'[Team] & " - " & 
    'Optiven R_E$Marketer Targets'[Region] & ")" & 
    " - " & FORMAT([Performance], "#,##0.0") & "%",
    UNICHAR(10)
)

Active Marketers by Team = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
SUMMARIZE(
    FILTER(
        'Optiven R_E$Marketer Targets',
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    ),
    'Optiven R_E$Marketer Targets'[Team],
    "Active Count", DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    "Team Performance", 
    DIVIDE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
        0
    ) * 100
)

Active Marketers by Region = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
RETURN
SUMMARIZE(
    FILTER(
        'Optiven R_E$Marketer Targets',
        YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
        MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
    ),
    'Optiven R_E$Marketer Targets'[Region],
    "Active Count", DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    "Region Performance", 
    DIVIDE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
        0
    ) * 100
)

Active Marketers Summary = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR TotalActive = [Active Marketers Count]
VAR TeamBreakdown = [Active Marketers by Team]
VAR RegionBreakdown = [Active Marketers by Region]

RETURN
"ACTIVE MARKETERS SUMMARY" & UNICHAR(10) & UNICHAR(10) &
"Total Active Marketers: " & FORMAT(TotalActive, "#,##0") & UNICHAR(10) & UNICHAR(10) &
"Team Distribution:" & UNICHAR(10) &
CONCATENATEX(
    TeamBreakdown,
    "• " & 'Optiven R_E$Marketer Targets'[Team] & ": " & 
    FORMAT([Active Count], "#,##0") & " marketers" & 
    " (" & FORMAT([Team Performance], "#,##0.0") & "%)" & UNICHAR(10),
    ""
) & UNICHAR(10) &
"Regional Distribution:" & UNICHAR(10) &
CONCATENATEX(
    RegionBreakdown,
    "• " & 'Optiven R_E$Marketer Targets'[Region] & ": " & 
    FORMAT([Active Count], "#,##0") & " marketers" & 
    " (" & FORMAT([Region Performance], "#,##0.0") & "%)" & UNICHAR(10),
    ""
)

// Month over Month Comparison Measures
MoM Comparison = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR CurrentMIB = 
    CALCULATE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        FILTER(
            ALLEXCEPT(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name]
            ),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )
VAR PreviousMIB = 
    CALCULATE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        FILTER(
            ALLEXCEPT(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name]
            ),
            'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
        )
    )
VAR Difference = CurrentMIB - PreviousMIB
RETURN
IF(
    Difference >= 0,
    "+" & FORMAT(Difference, "#,##0,,") & "M",
    FORMAT(Difference, "#,##0,,") & "M"
)

MoM Growth % = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR CurrentMIB = 
    CALCULATE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        FILTER(
            ALLEXCEPT(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name]
            ),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )
VAR PreviousMIB = 
    CALCULATE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        FILTER(
            ALLEXCEPT(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name]
            ),
            'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
        )
    )
RETURN
FORMAT(
    DIVIDE(CurrentMIB - PreviousMIB, PreviousMIB, 0) * 100,
    "+#,##0.0" & "%"
)

MoM Performance Change = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR CurrentPerf = 
    CALCULATE(
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100,
        FILTER(
            ALLEXCEPT(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name]
            ),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )
VAR PreviousPerf = 
    CALCULATE(
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100,
        FILTER(
            ALLEXCEPT(
                'Optiven R_E$Marketer Targets',
                'Optiven R_E$Marketer Targets'[Marketer Name]
            ),
            'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
        )
    )
RETURN
FORMAT(
    CurrentPerf - PreviousPerf,
    "+#,##0.0" & "%"
)

MoM Comparison Summary = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR CurrentMIB = [Current Period Achievement]
VAR PreviousMIB = [Previous Period Achievement]
VAR CurrentPerf = [Current Period Performance]
VAR PreviousPerf = [Previous Period Performance]
VAR Growth = CurrentMIB - PreviousMIB
VAR GrowthPct = DIVIDE(Growth, PreviousMIB, 0) * 100
VAR PerfChange = CurrentPerf - PreviousPerf

RETURN
"MONTH OVER MONTH COMPARISON" & UNICHAR(10) & UNICHAR(10) &
"Current Period: " & FORMAT(CurrentPeriodStart, "mmmm yyyy") & UNICHAR(10) &
"Previous Period: " & FORMAT(PreviousPeriodStart, "mmmm yyyy") & UNICHAR(10) & UNICHAR(10) &
"MIB Achievement:" & UNICHAR(10) &
"• Current: KES " & FORMAT(CurrentMIB, "#,##0,,M") & UNICHAR(10) &
"• Previous: KES " & FORMAT(PreviousMIB, "#,##0,,M") & UNICHAR(10) &
"• Change: " & FORMAT(Growth, "+#,##0,,M") & 
    " (" & FORMAT(GrowthPct, "+#,##0.0") & "%)" & UNICHAR(10) & UNICHAR(10) &
"Performance:" & UNICHAR(10) &
"• Current: " & FORMAT(CurrentPerf, "#,##0.0") & "%" & UNICHAR(10) &
"• Previous: " & FORMAT(PreviousPerf, "#,##0.0") & "%" & UNICHAR(10) &
"• Change: " & FORMAT(PerfChange, "+#,##0.0") & "%" & UNICHAR(10) & UNICHAR(10) &
"Status: " & 
    SWITCH(
        TRUE(),
        GrowthPct > 10, "Strong Growth 📈",
        GrowthPct > 0, "Positive Growth ↗️",
        GrowthPct = 0, "No Change ➡️",
        GrowthPct > -10, "Slight Decline ↘️",
        "Significant Decline 📉"
    )

Performance Comparison Card = 
VAR CurrentPerf = [Current Period Performance]
VAR PrevPerf = [Previous Period Performance]
VAR PerfChange = CurrentPerf - PrevPerf
VAR TrendIcon = 
    SWITCH(
        TRUE(),
        PerfChange > 5, "📈",
        PerfChange > 0, "↗️",
        PerfChange = 0, "➡️",
        PerfChange > -5, "↘️",
        "📉"
    )
RETURN
"📈 Current Period Performance" & UNICHAR(10) &
FORMAT(CurrentPerf, "#,##0.0") & "%" & UNICHAR(10) & UNICHAR(10) &
"Target Achievement" & UNICHAR(10) &
TrendIcon & " " & 
IF(
    PerfChange >= 0,
    "+" & FORMAT(PerfChange, "#,##0.0"),
    FORMAT(PerfChange, "#,##0.0")
) & "% vs last month"

MIB Achievement Card = 
VAR CurrentMIB = [Current Period Achievement]
VAR CurrentTarget = [Current Period Total Target]
VAR PreviousMIB = [Previous Period Achievement]
VAR MIBChange = CurrentMIB - PreviousMIB
VAR ChangeIcon = 
    SWITCH(
        TRUE(),
        MIBChange > 0, "💰",
        MIBChange = 0, "➡️",
        "📉"
    )
RETURN
"🎯 Total MIB Achieved" & UNICHAR(10) &
"KES " & FORMAT(CurrentMIB, "#,##0.0,,M") & UNICHAR(10) &
"of KES " & FORMAT(CurrentTarget, "#,##0.0,,M") & " target" & UNICHAR(10) &
ChangeIcon & " KES " & 
IF(
    MIBChange >= 0,
    FORMAT(ABS(MIBChange), "#,##0.0,,M") & " increase",
    FORMAT(ABS(MIBChange), "#,##0.0,,M") & " decrease"
)

Previous Period Target Gap = 
VAR CurrentPeriodStart = MAX('Optiven R_E$Marketer Targets'[Month Start Date])
VAR PreviousPeriodStart = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < CurrentPeriodStart
        )
    )
VAR PreviousTarget = 
    CALCULATE(
        [Total Monthly Target],
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
VAR PreviousAchieved = 
    CALCULATE(
        [Total MIB Achieved],
        'Optiven R_E$Marketer Targets'[Month Start Date] = PreviousPeriodStart
    )
RETURN
PreviousTarget - PreviousAchieved

Target Gap Card = 
VAR CurrentGap = [Current Period Target Gap]
VAR CurrentTarget = [Current Period Total Target]
VAR PreviousGap = [Previous Period Target Gap]
VAR GapChange = CurrentGap - PreviousGap
VAR GapChangePct = DIVIDE(GapChange, PreviousGap, 0) * 100
VAR GapPct = DIVIDE(CurrentGap, CurrentTarget, 0) * 100
VAR TrendIcon = 
    SWITCH(
        TRUE(),
        GapChange < 0, "📈",  // Gap is decreasing (good)
        GapChange = 0, "➡️",  // No change
        "📉"                  // Gap is increasing (bad)
    )
RETURN
"⚠️ Target Gap" & UNICHAR(10) &
"KES " & FORMAT(ABS(CurrentGap), "#,##0.0,,M") & UNICHAR(10) &
FORMAT(ABS(GapPct), "#,##0.0") & "% below target" & UNICHAR(10) &
TrendIcon & " " & 
IF(
    GapChangePct <= 0,
    FORMAT(ABS(GapChangePct), "#,##0.0"),
    "-" & FORMAT(GapChangePct, "#,##0.0")
) & "% vs last month"

Employee Achievement Card = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))
VAR PreviousYearMonth = 
    CALCULATE(
        MAX('Optiven R_E$Marketer Targets'[Month Start Date]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            'Optiven R_E$Marketer Targets'[Month Start Date] < MAX('Optiven R_E$Marketer Targets'[Month Start Date])
        )
    )
VAR PreviousYearMonthNum = 
    YEAR(PreviousYearMonth) * 100 + MONTH(PreviousYearMonth)

// Current Period Calculations
VAR CurrentAbove100Count = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth &&
            DIVIDE(
                SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                0
            ) * 100 >= 100
        )
    )

// Previous Period Calculations
VAR PreviousAbove100Count = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = PreviousYearMonthNum &&
            DIVIDE(
                SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
                SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
                0
            ) * 100 >= 100
        )
    )

// Total Employees in Current Period
VAR TotalCurrentEmployees = 
    CALCULATE(
        DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
        FILTER(
            ALL('Optiven R_E$Marketer Targets'),
            YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
            MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth
        )
    )

VAR TotalExceedingCount = IF(ISBLANK(CurrentAbove100Count), 0, CurrentAbove100Count)
VAR TotalExceedingChange = IF(ISBLANK(CurrentAbove100Count), 0, CurrentAbove100Count) - 
                          IF(ISBLANK(PreviousAbove100Count), 0, PreviousAbove100Count)
VAR AchievementRate = 
    DIVIDE(
        TotalExceedingCount,
        TotalCurrentEmployees,
        0
    ) * 100

RETURN
"👥 Employee Achievement Rate" & UNICHAR(10) &
FORMAT(AchievementRate, "#,##0.0") & "%" & UNICHAR(10) & UNICHAR(10) &
"Employees at Target:" & UNICHAR(10) &
"• At or Above 100%: " & FORMAT(TotalExceedingCount, "#,##0") & 
" of " & FORMAT(TotalCurrentEmployees, "#,##0") & UNICHAR(10) & UNICHAR(10) &
"Change from Previous Period:" & UNICHAR(10) &
SWITCH(
    TRUE(),
    TotalExceedingChange > 0, "📈 +" & FORMAT(TotalExceedingChange, "#,##0") & " employees",
    TotalExceedingChange < 0, "📉 " & FORMAT(TotalExceedingChange, "#,##0") & " employees",
    "➡️ No change"
)

Overall Performance Donut = 
VAR CurrentYearMonth = 
    YEAR(MAX('Optiven R_E$Marketer Targets'[Month Start Date])) * 100 + 
    MONTH(MAX('Optiven R_E$Marketer Targets'[Month Start Date]))

// Calculate average performance excluding zero performers
VAR AveragePerformance = 
    CALCULATE(
        AVERAGEX(
            FILTER(
                'Optiven R_E$Marketer Targets',
                YEAR('Optiven R_E$Marketer Targets'[Month Start Date]) * 100 + 
                MONTH('Optiven R_E$Marketer Targets'[Month Start Date]) = CurrentYearMonth &&
                VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)]) > 0
            ),
            VALUE('Optiven R_E$Marketer Targets'[MIB Performance (_)])
        )
    )

// Calculate remaining percentage to 100%
VAR RemainingPercentage = IF(ISBLANK(AveragePerformance), 100, 100 - MIN(AveragePerformance, 100))

RETURN
UNION(
    ROW("Category", "Performance", "Value", AveragePerformance, "Color", "#27ae60"),
    ROW("Category", "Remaining", "Value", RemainingPercentage, "Color", "#e0e0e0")
)

// Base table measure for marketer performance
Marketer Performance Table = 
SUMMARIZECOLUMNS(
    'Optiven R_E$Marketer Targets'[Marketer Name],
    'Optiven R_E$Marketer Targets'[Team],
    'Optiven R_E$Marketer Targets'[Region],
    "Performance", 
    DIVIDE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
        0
    ) * 100,
    "MIB Achieved", 
    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
    "Monthly Target", 
    SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
    "Target Gap", 
    SUM('Optiven R_E$Marketer Targets'[Monthly Target]) - 
    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
    "Performance Status",
    SWITCH(
        TRUE(),
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 100, "Exceeding Target",
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 80, "Near Target",
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 50, "Average",
        "Below Target"
    ),
    "Period", FORMAT(MAX('Optiven R_E$Marketer Targets'[Month Start Date]), "mmmm yyyy")
)

Simple Performance Filter = 
IF(
    MAX('Optiven R_E$Marketer Targets'[MIB Performance (_)]) >= 85,
    1,
    0
)

Direct Performance = 
MAX('Optiven R_E$Marketer Targets'[MIB Performance (_)])

Filtered Marketer Performance = 
VAR SelectedPeriodLabel = SELECTEDVALUE(DateDimension[TargetPeriodLabel])
VAR SelectedPeriodStart = 
    CALCULATE(
        MIN(DateDimension[TargetPeriodStart]),
        DateDimension[TargetPeriodLabel] = SelectedPeriodLabel
    )
VAR SelectedPeriodEnd = 
    CALCULATE(
        MAX(DateDimension[TargetPeriodEnd]),
        DateDimension[TargetPeriodLabel] = SelectedPeriodLabel
    )

RETURN
ADDCOLUMNS(
    SUMMARIZE(
        FILTER(
            'Optiven R_E$Marketer Targets',
            'Optiven R_E$Marketer Targets'[Month Start Date] >= SelectedPeriodStart &&
            'Optiven R_E$Marketer Targets'[Month End Date] <= SelectedPeriodEnd
        ),
        'Optiven R_E$Marketer Targets'[Marketer Name],
        'Optiven R_E$Marketer Targets'[Team],
        'Optiven R_E$Marketer Targets'[Region]
    ),
    "Performance", 
    CALCULATE(
        AVERAGE('Optiven R_E$Marketer Targets'[MIB Performance (_)])
    ),
    "MIB Achieved", 
    CALCULATE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved])
    ),
    "Monthly Target", 
    CALCULATE(
        SUM('Optiven R_E$Marketer Targets'[Monthly Target])
    ),
    "Period", SelectedPeriodLabel
)

// === MARKETER PERFORMANCE METRICS TABLE ===
Marketer Performance Metrics = 
SUMMARIZECOLUMNS(
    'Optiven R_E$Marketer Targets'[Marketer Name],
    'Optiven R_E$Marketer Targets'[Team],
    'Optiven R_E$Marketer Targets'[Region],
    'Optiven R_E$Marketer Targets'[Month Start Date],
    "Performance %", 
    DIVIDE(
        SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
        SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
        0
    ) * 100,
    "MIB Achieved", 
    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
    "Monthly Target", 
    SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
    "Target Gap", 
    SUM('Optiven R_E$Marketer Targets'[Monthly Target]) - 
    SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
    "Performance Status",
    SWITCH(
        TRUE(),
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 100, "Exceeding Target",
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 80, "Near Target",
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 50, "Average",
        "Below Target"
    ),
    "Period", FORMAT(MAX('Optiven R_E$Marketer Targets'[Month Start Date]), "mmmm yyyy")
)

// === PERFORMANCE METRICS MEASURES ===
Performance Metrics Total Marketers = 
DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name])

Performance Metrics Total Target = 
SUM('Optiven R_E$Marketer Targets'[Monthly Target])

Performance Metrics Total Achieved = 
SUM('Optiven R_E$Marketer Targets'[MIB Achieved])

Performance Metrics Overall % = 
DIVIDE(
    [Performance Metrics Total Achieved],
    [Performance Metrics Total Target],
    0
) * 100

Performance Metrics Exceeding Target = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        'Optiven R_E$Marketer Targets',
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 100
    )
)

Performance Metrics Near Target = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        'Optiven R_E$Marketer Targets',
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 >= 80 &&
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 < 100
    )
)

Performance Metrics Below Target = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Marketer Targets'[Marketer Name]),
    FILTER(
        'Optiven R_E$Marketer Targets',
        DIVIDE(
            SUM('Optiven R_E$Marketer Targets'[MIB Achieved]),
            SUM('Optiven R_E$Marketer Targets'[Monthly Target]),
            0
        ) * 100 < 50
    )
)

Performance Metrics Achievement Rate = 
DIVIDE(
    [Performance Metrics Exceeding Target],
    [Performance Metrics Total Marketers],
    0
) * 100