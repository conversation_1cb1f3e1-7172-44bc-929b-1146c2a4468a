// =====================================================
// REVENUE DASHBOARD DAX MEASURES
// For Optiven Real Estate Investment Tracking
// =====================================================

// =====================================================
// 1. PAYMENT TRACKING MEASURES
// =====================================================

// Total Payments by Lead File
Total Payments = 
CALCULATE(
    SUM('Optiven R_E$Receipt Line'[Amount]),
    'Optiven R_E$Receipt Line'[Posted] = 1,
    'Optiven R_E$Receipt Line'[Cancelled] = 0
)

// Payment Balance per Lead File
Payment Balance = 
VAR BookingAmount = SUM('Optiven R_E$Investment Booking'[Property Price])
VAR TotalPaid = 
    CALCULATE(
        SUM('Optiven R_E$Receipt Line'[Amount]),
        FILTER(
            'Optiven R_E$Receipt Line',
            'Optiven R_E$Receipt Line'[Lead File No_] = MAX('Optiven R_E$Investment Booking'[Booking No])
            && 'Optiven R_E$Receipt Line'[Posted] = 1
            && 'Optiven R_E$Receipt Line'[Cancelled] = 0
        )
    )
RETURN
    IF(ISBLANK(BookingAmount), BLANK(), BookingAmount - IF(ISBLANK(TotalPaid), 0, TotalPaid))

// Collection Rate %
Collection Rate % = 
VAR TotalBooked = [Total Revenue]
VAR TotalCollected = [Total Payments]
RETURN
    IF(
        TotalBooked = 0,
        BLANK(),
        DIVIDE(TotalCollected, TotalBooked, 0) * 100
    )

// Average Payment per Customer
Avg Payment per Customer = 
AVERAGEX(
    VALUES('Optiven R_E$Investment Booking'[Member No]),
    [Total Payments]
)

// Total Outstanding Amount
Total Outstanding = 
SUMX(
    'Optiven R_E$Investment Booking',
    VAR Balance = [Payment Balance]
    RETURN IF(Balance > 0, Balance, 0)
)

// =====================================================
// 2. REVENUE TREND MEASURES
// =====================================================

// Total Revenue (Base Measure)
Total Revenue = 
CALCULATE(
    SUM('Optiven R_E$Investment Booking'[Property Price]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Revenue by Period (using DateDimension)
Revenue by Period = 
CALCULATE(
    [Total Revenue],
    USERELATIONSHIP(DateDimension[Date], 'Optiven R_E$Investment Booking'[Booking Date])
)

// Collections by Period
Collections by Period = 
CALCULATE(
    [Total Payments],
    USERELATIONSHIP(DateDimension[Date], 'Optiven R_E$Receipt Line'[Payment Date])
)

// Revenue Growth % (Period over Period)
Revenue Growth % = 
VAR CurrentPeriodRevenue = [Revenue by Period]
VAR PreviousPeriodRevenue = 
    CALCULATE(
        [Revenue by Period],
        DATEADD(DateDimension[Date], -1, MONTH)
    )
RETURN
    IF(
        PreviousPeriodRevenue = 0,
        BLANK(),
        DIVIDE(
            CurrentPeriodRevenue - PreviousPeriodRevenue,
            PreviousPeriodRevenue,
            0
        ) * 100
    )

// Running Total Revenue
Running Total Revenue = 
CALCULATE(
    [Total Revenue],
    FILTER(
        ALL(DateDimension),
        DateDimension[Date] <= MAX(DateDimension[Date])
    )
)

// Running Total Collections
Running Total Collections = 
CALCULATE(
    [Total Payments],
    FILTER(
        ALL(DateDimension),
        DateDimension[Date] <= MAX(DateDimension[Date])
    )
)

// =====================================================
// 3. PROJECT PERFORMANCE MEASURES
// =====================================================

// Revenue by Project
Revenue by Project = 
CALCULATE(
    SUM('Optiven R_E$Investment Booking'[Property Price]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Project Units Sold
Project Units Sold = 
CALCULATE(
    COUNTROWS('Optiven R_E$Investment Booking'),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Project Ranking by Revenue
Project Revenue Rank = 
RANKX(
    ALL('Optiven R_E$Investment Booking'[Project Name]),
    [Revenue by Project],
    ,
    DESC,
    Dense
)

// Is Top 5 Project
Is Top 5 Project = 
IF([Project Revenue Rank] <= 5, 1, 0)

// Is Bottom 5 Project
Is Bottom 5 Project = 
VAR TotalProjects = 
    CALCULATE(
        COUNTROWS(VALUES('Optiven R_E$Investment Booking'[Project Name])),
        ALL('Optiven R_E$Investment Booking')
    )
RETURN
    IF([Project Revenue Rank] >= TotalProjects - 4, 1, 0)

// Average Plot Price by Project
Avg Plot Price by Project = 
CALCULATE(
    AVERAGE('Optiven R_E$Investment Booking'[Property Price]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Project Collection Rate
Project Collection Rate = 
VAR ProjectRevenue = [Revenue by Project]
VAR ProjectCollections = 
    CALCULATE(
        SUM('Optiven R_E$Receipt Line'[Amount]),
        FILTER(
            'Optiven R_E$Receipt Line',
            'Optiven R_E$Receipt Line'[Project No_] = MAX('Optiven R_E$Investment Booking'[Project No_])
            && 'Optiven R_E$Receipt Line'[Posted] = 1
            && 'Optiven R_E$Receipt Line'[Cancelled] = 0
        )
    )
RETURN
    IF(
        ProjectRevenue = 0,
        BLANK(),
        DIVIDE(ProjectCollections, ProjectRevenue, 0) * 100
    )

// =====================================================
// 4. CUSTOMER PAYMENT BEHAVIOR
// =====================================================

// Days to First Payment
Days to First Payment = 
VAR BookingDate = MAX('Optiven R_E$Investment Booking'[Booking Date])
VAR FirstPaymentDate = 
    CALCULATE(
        MIN('Optiven R_E$Receipt Line'[Payment Date]),
        FILTER(
            'Optiven R_E$Receipt Line',
            'Optiven R_E$Receipt Line'[Lead File No_] = MAX('Optiven R_E$Investment Booking'[Booking No])
            && 'Optiven R_E$Receipt Line'[Posted] = 1
        )
    )
RETURN
    IF(
        ISBLANK(BookingDate) || ISBLANK(FirstPaymentDate),
        BLANK(),
        DATEDIFF(BookingDate, FirstPaymentDate, DAY)
    )

// Payment Frequency (Number of Payments)
Payment Frequency = 
CALCULATE(
    COUNTROWS('Optiven R_E$Receipt Line'),
    'Optiven R_E$Receipt Line'[Posted] = 1,
    'Optiven R_E$Receipt Line'[Cancelled] = 0
)

// Customers with Full Payment
Fully Paid Customers = 
COUNTROWS(
    FILTER(
        'Optiven R_E$Investment Booking',
        [Payment Balance] <= 0
    )
)

// Customers with Outstanding Balance
Customers with Balance = 
COUNTROWS(
    FILTER(
        'Optiven R_E$Investment Booking',
        [Payment Balance] > 0
    )
)

// Average Days to Payment
Avg Days to Payment = 
AVERAGEX(
    'Optiven R_E$Investment Booking',
    [Days to First Payment]
)

// =====================================================
// 5. YEAR-TO-DATE (YTD) MEASURES
// =====================================================

// Total Revenue YTD
Total Revenue YTD = 
CALCULATE(
    [Total Revenue],
    DATESYTD(DateDimension[Date])
)

// Total Collections YTD
Total Collections YTD = 
CALCULATE(
    [Total Payments],
    DATESYTD(DateDimension[Date])
)

// Units Sold YTD
Units Sold YTD = 
CALCULATE(
    COUNTROWS('Optiven R_E$Investment Booking'),
    'Optiven R_E$Investment Booking'[Posted] = 1,
    DATESYTD(DateDimension[Date])
)

// New Customers YTD
New Customers YTD = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    'Optiven R_E$Investment Booking'[Posted] = 1,
    DATESYTD(DateDimension[Date])
)

// =====================================================
// 6. MONTH-TO-DATE (MTD) MEASURES
// =====================================================

// Total Revenue MTD
Total Revenue MTD = 
CALCULATE(
    [Total Revenue],
    DATESMTD(DateDimension[Date])
)

// Total Collections MTD
Total Collections MTD = 
CALCULATE(
    [Total Payments],
    DATESMTD(DateDimension[Date])
)

// =====================================================
// 7. SALES PERFORMANCE MEASURES
// =====================================================

// Revenue by Sales Person
Revenue by Sales Person = 
CALCULATE(
    SUM('Optiven R_E$Investment Booking'[Property Price]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Sales Person Ranking
Sales Person Rank = 
RANKX(
    ALL('Optiven R_E$Investment Booking'[Sales Person Name]),
    [Revenue by Sales Person],
    ,
    DESC,
    Dense
)

// Sales Team Performance
Sales Team Performance = 
CALCULATE(
    SUM('Optiven R_E$Investment Booking'[Property Price]),
    FILTER(
        'Optiven R_E$Investment Booking',
        'Optiven R_E$Investment Booking'[Global Dimension 1 Code] = SELECTEDVALUE('Optiven R_E$Investment Booking'[Global Dimension 1 Code])
        && 'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// =====================================================
// 8. REGIONAL PERFORMANCE MEASURES
// =====================================================

// Revenue by Customer Region
Revenue by Region = 
CALCULATE(
    SUM('Optiven R_E$Investment Booking'[Property Price]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Collections by Region
Collections by Region = 
CALCULATE(
    [Total Payments],
    FILTER(
        'Optiven R_E$Receipt Line',
        'Optiven R_E$Receipt Line'[MIBRegion] = SELECTEDVALUE('Optiven R_E$Investment Booking'[Customer Region])
    )
)

// =====================================================
// 9. LEAD SOURCE ANALYSIS
// =====================================================

// Revenue by Lead Source
Revenue by Lead Source = 
CALCULATE(
    SUM('Optiven R_E$Investment Booking'[Property Price]),
    FILTER(
        'Optiven R_E$Investment Booking',
        'Optiven R_E$Investment Booking'[Lead Source] = SELECTEDVALUE('Optiven R_E$Investment Booking'[Lead Source])
        && 'Optiven R_E$Investment Booking'[Posted] = 1
    )
)

// Conversion Rate by Lead Source
Lead Source Conversion Rate = 
VAR TotalLeads = COUNTROWS('Optiven R_E$Investment Booking')
VAR ConvertedLeads = 
    CALCULATE(
        COUNTROWS('Optiven R_E$Investment Booking'),
        'Optiven R_E$Investment Booking'[Posted] = 1,
        [Total Payments] > 0
    )
RETURN
    DIVIDE(ConvertedLeads, TotalLeads, 0) * 100

// =====================================================
// 10. KEY PERFORMANCE INDICATORS (KPIs)
// =====================================================

// Total Active Projects
Total Active Projects = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Project No_]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Total Active Customers
Total Active Customers = 
CALCULATE(
    DISTINCTCOUNT('Optiven R_E$Investment Booking'[Member No]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Average Deal Size
Average Deal Size = 
CALCULATE(
    AVERAGE('Optiven R_E$Investment Booking'[Property Price]),
    'Optiven R_E$Investment Booking'[Posted] = 1
)

// Collection Efficiency Score
Collection Efficiency = 
VAR CollectionRate = [Collection Rate %]
VAR AvgDaysToPayment = [Avg Days to Payment]
VAR TargetDays = 30 // Adjust based on your business rules
RETURN
    IF(
        ISBLANK(CollectionRate) || ISBLANK(AvgDaysToPayment),
        BLANK(),
        (CollectionRate * 0.7) + ((1 - (AvgDaysToPayment / TargetDays)) * 30)
    )

// Revenue per Customer
Revenue per Customer = 
DIVIDE(
    [Total Revenue],
    [Total Active Customers],
    0
)

// =====================================================
// 11. TARGET PERIOD MEASURES (21st-20th)
// =====================================================

// Revenue Current Target Period
Revenue Current Period = 
CALCULATE(
    [Total Revenue],
    FILTER(
        DateDimension,
        DateDimension[TargetPeriodSort] = MAX(DateDimension[TargetPeriodSort])
    )
)

// Collections Current Target Period
Collections Current Period = 
CALCULATE(
    [Total Payments],
    FILTER(
        DateDimension,
        DateDimension[TargetPeriodSort] = MAX(DateDimension[TargetPeriodSort])
    )
)

// Revenue Previous Target Period
Revenue Previous Period = 
CALCULATE(
    [Total Revenue],
    FILTER(
        DateDimension,
        DateDimension[TargetPeriodSort] = MAX(DateDimension[TargetPeriodSort]) - 1
    )
)

// Target Period Growth %
Target Period Growth % = 
VAR CurrentRevenue = [Revenue Current Period]
VAR PreviousRevenue = [Revenue Previous Period]
RETURN
    IF(
        PreviousRevenue = 0,
        BLANK(),
        DIVIDE(CurrentRevenue - PreviousRevenue, PreviousRevenue, 0) * 100
    ) 